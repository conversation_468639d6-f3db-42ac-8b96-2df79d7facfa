import * as vscode from "vscode";
import type { ILogger } from "../services/logger";
import type { IContextManager } from "../services/contextManager";
import {
  type IContextRetrievalParams,
  type IContextRetrievalResult,
  type IRuleContent,
  CATEGORY_INFO,
  RuleSystemError,
  type IRuleSystemError,
} from "../types/rules";

/**
 * Parameters for context retrieval tool
 */
interface IContextRetrievalToolParams {
  /** User query for context retrieval */
  query: string;
  /** Maximum number of rules to retrieve (default: 5) */
  maxRules?: number;
  /** Specific service to search in */
  service?: string;
  /** Include rule content in response (default: true) */
  includeContent?: boolean;
}

/**
 * Chat tool for retrieving relevant rule context from GitHub repositories
 */
export class ContextRetrievalTool implements vscode.LanguageModelTool<IContextRetrievalToolParams> {
  private readonly logger: ILogger;
  private readonly contextManager: IContextManager;

  constructor(logger: ILogger, contextManager: IContextManager) {
    this.logger = logger;
    this.contextManager = contextManager;
  }

  /**
   * Invoke the context retrieval tool
   */
  async invoke(
    options: vscode.LanguageModelToolInvocationOptions<IContextRetrievalToolParams>,
    token: vscode.CancellationToken
  ): Promise<vscode.LanguageModelToolResult> {
    try {
      this.logger.info(`Context retrieval tool invoked with query: "${options.input.query}"`);

      // Validate input parameters
      const validationError = this.validateInput(options.input);
      if (validationError) {
        return new vscode.LanguageModelToolResult([
          new vscode.LanguageModelTextPart(validationError),
        ]);
      }

      // Check for cancellation
      if (token.isCancellationRequested) {
        return new vscode.LanguageModelToolResult([
          new vscode.LanguageModelTextPart("Context retrieval was cancelled."),
        ]);
      }

      // Prepare retrieval parameters
      const params: IContextRetrievalParams = {
        query: options.input.query.trim(),
        maxRules: options.input.maxRules || 5,
        service: options.input.service,
        includeContent: options.input.includeContent !== false,
      };

      // Retrieve context
      const result = await this.contextManager.retrieveContext(params);

      // Check for cancellation after retrieval
      if (token.isCancellationRequested) {
        return new vscode.LanguageModelToolResult([
          new vscode.LanguageModelTextPart("Context retrieval was cancelled."),
        ]);
      }

      // Format and return results
      const formattedResult = this.formatRetrievalResult(result);
      
      this.logger.info(`Context retrieval completed: ${result.rules.length} rules retrieved`);
      
      return new vscode.LanguageModelToolResult([
        new vscode.LanguageModelTextPart(formattedResult),
      ]);

    } catch (error) {
      this.logger.error('Error in context retrieval tool:', error);
      return this.handleError(error as Error);
    }
  }

  /**
   * Prepare tool invocation with user confirmation
   */
  async prepareInvocation(
    options: vscode.LanguageModelToolInvocationPrepareOptions<IContextRetrievalToolParams>,
    _token: vscode.CancellationToken
  ): Promise<vscode.PreparedToolInvocation> {
    const query = options.input.query?.trim() || '';
    const maxRules = options.input.maxRules || 5;
    const service = options.input.service;

    let confirmationMessage = `Retrieve development rules for: **"${query}"**`;
    
    if (service) {
      confirmationMessage += `\n- Service: ${service}`;
    }
    
    confirmationMessage += `\n- Max rules: ${maxRules}`;

    const confirmationMessages = {
      title: "Retrieve Development Rules",
      message: new vscode.MarkdownString(confirmationMessage),
    };

    return {
      invocationMessage: `Searching for relevant development rules...`,
      confirmationMessages,
    };
  }

  /**
   * Validate input parameters
   */
  private validateInput(input: IContextRetrievalToolParams): string | null {
    if (!input.query || typeof input.query !== 'string') {
      return "❌ **Error**: Query parameter is required and must be a string.";
    }

    if (input.query.trim().length === 0) {
      return "❌ **Error**: Query cannot be empty.";
    }

    if (input.query.trim().length < 3) {
      return "❌ **Error**: Query must be at least 3 characters long.";
    }

    if (input.maxRules !== undefined) {
      if (typeof input.maxRules !== 'number' || input.maxRules < 1 || input.maxRules > 20) {
        return "❌ **Error**: maxRules must be a number between 1 and 20.";
      }
    }

    if (input.service !== undefined && typeof input.service !== 'string') {
      return "❌ **Error**: service parameter must be a string.";
    }

    return null;
  }

  /**
   * Format retrieval result for display
   */
  private formatRetrievalResult(result: IContextRetrievalResult): string {
    if (result.rules.length === 0) {
      return this.formatNoResultsMessage(result);
    }

    const parts: string[] = [];

    // Header with summary
    parts.push(`# 📋 Development Rules Context`);
    parts.push(`**Query**: "${result.query}"`);
    parts.push(`**Found**: ${result.rules.length} rules (${result.totalFound} total matches)`);
    
    if (result.repositoryStructure.repositoryUrl) {
      parts.push(`**Repository**: [${result.repositoryStructure.repositoryUrl.split('/').slice(-2).join('/')}](${result.repositoryStructure.repositoryUrl})`);
    }
    
    parts.push(''); // Empty line

    // Format each rule
    for (let i = 0; i < result.rules.length; i++) {
      const rule = result.rules[i];
      const score = result.scores[i];
      
      parts.push(this.formatRule(rule, score, i + 1));
      parts.push(''); // Empty line between rules
    }

    // Footer with repository info
    if (result.repositoryStructure.description) {
      parts.push('---');
      parts.push(`**Repository Description**: ${result.repositoryStructure.description}`);
    }

    return parts.join('\n');
  }

  /**
   * Format a single rule for display
   */
  private formatRule(rule: IRuleContent, score: number, index: number): string {
    const categoryInfo = CATEGORY_INFO[rule.category];
    const parts: string[] = [];

    // Rule header
    parts.push(`## ${index}. ${categoryInfo.icon} ${rule.name}`);
    
    // Metadata
    const metadata: string[] = [];
    metadata.push(`**Category**: ${categoryInfo.name}`);
    metadata.push(`**Service**: ${rule.service}`);
    
    if (rule.version) {
      metadata.push(`**Version**: ${rule.version}`);
    }
    
    if (rule.specificity) {
      metadata.push(`**Scope**: ${rule.specificity}`);
    }
    
    metadata.push(`**Relevance**: ${Math.round(score)}%`);
    metadata.push(`**File**: \`${rule.filename}\``);
    
    parts.push(metadata.join(' • '));

    // Summary
    if (rule.summary) {
      parts.push('');
      parts.push(`**Summary**: ${rule.summary}`);
    }

    // Content (truncated if too long)
    if (rule.content) {
      parts.push('');
      parts.push('**Content**:');
      
      const maxContentLength = 1000;
      let content = rule.content;
      
      if (content.length > maxContentLength) {
        content = content.substring(0, maxContentLength) + '\n\n*[Content truncated...]*';
      }
      
      parts.push('```markdown');
      parts.push(content);
      parts.push('```');
    }

    return parts.join('\n');
  }

  /**
   * Format message when no results are found
   */
  private formatNoResultsMessage(result: IContextRetrievalResult): string {
    const parts: string[] = [];
    
    parts.push(`# 🔍 No Development Rules Found`);
    parts.push(`**Query**: "${result.query}"`);
    parts.push('');
    parts.push('No relevant development rules were found for your query.');
    parts.push('');
    parts.push('**Suggestions**:');
    parts.push('- Try using different keywords');
    parts.push('- Check if the repository contains rule files');
    parts.push('- Verify the repository structure includes a `main.mdc` file');
    parts.push('- Try broader search terms');
    
    if (result.repositoryStructure.totalRules > 0) {
      parts.push('');
      parts.push(`**Available**: ${result.repositoryStructure.totalRules} total rules across ${Object.keys(result.repositoryStructure.services).length} services`);
      
      // List available services
      const services = Object.keys(result.repositoryStructure.services);
      if (services.length > 0) {
        parts.push('**Services**: ' + services.join(', '));
      }
    }

    return parts.join('\n');
  }

  /**
   * Handle errors and return appropriate error message
   */
  private handleError(error: Error): vscode.LanguageModelToolResult {
    let errorMessage = "❌ **Error retrieving development rules**\n\n";

    if (this.isRuleSystemError(error)) {
      switch (error.type) {
        case RuleSystemError.REPOSITORY_NOT_FOUND:
          errorMessage += "**Repository not found or not accessible.**\n";
          errorMessage += "Please ensure you have selected a valid repository.";
          break;
        case RuleSystemError.MAIN_MDC_NOT_FOUND:
          errorMessage += "**Repository structure file (main.mdc) not found.**\n";
          errorMessage += "The repository may not follow the expected structure.";
          break;
        case RuleSystemError.NETWORK_ERROR:
          errorMessage += "**Network error occurred.**\n";
          errorMessage += "Please check your internet connection and try again.";
          break;
        case RuleSystemError.PERMISSION_DENIED:
          errorMessage += "**Permission denied.**\n";
          errorMessage += "You may not have access to this repository.";
          break;
        default:
          errorMessage += `**${error.type}**: ${error.message}`;
      }
    } else {
      errorMessage += `**Unexpected error**: ${error.message}`;
    }

    errorMessage += "\n\nPlease try again or contact support if the issue persists.";

    return new vscode.LanguageModelToolResult([
      new vscode.LanguageModelTextPart(errorMessage),
    ]);
  }

  /**
   * Type guard to check if error is a rule system error
   */
  private isRuleSystemError(error: Error): error is IRuleSystemError {
    return 'type' in error && Object.values(RuleSystemError).includes((error as IRuleSystemError).type);
  }
}
